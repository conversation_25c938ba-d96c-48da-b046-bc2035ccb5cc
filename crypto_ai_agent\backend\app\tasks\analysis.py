import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from sqlalchemy import func
from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.core.config import settings
from app.db.session import SessionLocal
from app.models.blockchain_data import Blockchain, Token, Transaction, Wallet, WalletToken, TokenPrice
from app.models.analysis import Strategy, Signal, Indicator, SignalIndicator, AnalysisResult, WhaleActivity, StrategyType

logger = logging.getLogger(__name__)


@celery_app.task(name="app.tasks.analysis.run_daily_analysis")
def run_daily_analysis() -> Dict[str, Any]:
    """运行每日分析任务"""
    results = {}
    
    # 获取活跃策略
    db = SessionLocal()
    try:
        active_strategies = db.query(Strategy).filter(Strategy.is_active == True).all()
        
        for strategy in active_strategies:
            try:
                # 根据策略类型运行相应的分析
                if strategy.strategy_type == StrategyType.WHALE_TRACKING:
                    result = analyze_whale_activity.delay(strategy.id)
                elif strategy.strategy_type == StrategyType.DEVELOPER_ACTIVITY:
                    result = analyze_developer_activity.delay(strategy.id)
                elif strategy.strategy_type == StrategyType.ONCHAIN_ACTIVITY:
                    result = analyze_onchain_activity.delay(strategy.id)
                elif strategy.strategy_type == StrategyType.SMART_CONTRACT_INTERACTION:
                    result = analyze_smart_contract_interaction.delay(strategy.id)
                elif strategy.strategy_type == StrategyType.CROSS_CHAIN_FLOW:
                    result = analyze_cross_chain_flow.delay(strategy.id)
                elif strategy.strategy_type == StrategyType.CUSTOM:
                    result = run_custom_strategy.delay(strategy.id)
                else:
                    logger.warning(f"未知策略类型: {strategy.strategy_type}")
                    continue
                
                results[strategy.name] = {"task_id": result.id, "status": "started"}
            except Exception as e:
                logger.error(f"运行策略{strategy.name}失败: {e}")
                results[strategy.name] = {"status": "failed", "error": str(e)}
    except Exception as e:
        logger.error(f"获取活跃策略失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()
    
    return {"status": "success", "strategies": results}


@celery_app.task(name="app.tasks.analysis.analyze_whale_activity")
def analyze_whale_activity(strategy_id: int) -> Dict[str, Any]:
    """分析鲸鱼活动策略"""
    db = SessionLocal()
    try:
        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        if not strategy:
            return {"status": "failed", "error": "策略不存在"}
        
        # 获取策略参数
        params = strategy.parameters or {}
        min_transaction_value = params.get("min_transaction_value", 100000)  # 默认10万美元
        time_window_days = params.get("time_window_days", 7)  # 默认7天
        min_transactions = params.get("min_transactions", 3)  # 默认至少3笔交易
        
        # 计算时间窗口
        end_date = datetime.now()
        start_date = end_date - timedelta(days=time_window_days)
        
        # 获取大额交易
        large_transactions = db.query(
            Transaction.token_id,
            func.count(Transaction.id).label("transaction_count"),
            func.sum(TokenPrice.price_usd * Transaction.value).label("total_value_usd")
        ).join(
            Token, Transaction.token_id == Token.id
        ).join(
            TokenPrice, TokenPrice.token_id == Token.id
        ).filter(
            Transaction.timestamp.between(start_date, end_date),
            TokenPrice.price_usd * Transaction.value >= min_transaction_value
        ).group_by(
            Transaction.token_id
        ).having(
            func.count(Transaction.id) >= min_transactions
        ).all()
        
        # 生成信号
        signals_created = []
        for token_id, tx_count, total_value in large_transactions:
            # 计算信号强度和置信度
            strength = min(1.0, total_value / 1000000)  # 根据交易总额计算强度
            confidence = min(1.0, tx_count / 10)  # 根据交易数量计算置信度
            
            # 创建信号
            signal = Signal(
                strategy_id=strategy.id,
                token_id=token_id,
                signal_type="buy",
                strength=strength,
                confidence=confidence,
                timestamp=datetime.now(),
                expiration=datetime.now() + timedelta(days=7),
                metadata={
                    "transaction_count": tx_count,
                    "total_value_usd": total_value,
                    "time_window_days": time_window_days
                }
            )
            db.add(signal)
            signals_created.append({
                "token_id": token_id,
                "strength": strength,
                "confidence": confidence
            })
        
        db.commit()
        return {
            "status": "success",
            "strategy": strategy.name,
            "signals_created": len(signals_created),
            "signals": signals_created
        }
    except Exception as e:
        db.rollback()
        logger.error(f"分析鲸鱼活动失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.analysis.analyze_developer_activity")
def analyze_developer_activity(strategy_id: int) -> Dict[str, Any]:
    """分析开发者活动策略"""
    # 这里需要集成GitHub API或其他开发者活动数据源
    return {"status": "not_implemented", "message": "开发者活动分析尚未实现"}


@celery_app.task(name="app.tasks.analysis.analyze_onchain_activity")
def analyze_onchain_activity(strategy_id: int) -> Dict[str, Any]:
    """分析链上活动策略"""
    db = SessionLocal()
    try:
        strategy = db.query(Strategy).filter(Strategy.id == strategy_id).first()
        if not strategy:
            return {"status": "failed", "error": "策略不存在"}
        
        # 获取策略参数
        params = strategy.parameters or {}
        time_window_days = params.get("time_window_days", 7)  # 默认7天
        min_tx_growth = params.get("min_tx_growth", 0.2)  # 默认20%的交易增长
        min_address_growth = params.get("min_address_growth", 0.1)  # 默认10%的地址增长
        
        # 计算时间窗口
        now = datetime.now()
        period_end = now
        period_start = now - timedelta(days=time_window_days)
        prev_period_end = period_start
        prev_period_start = prev_period_end - timedelta(days=time_window_days)
        
        # 获取所有活跃代币
        tokens = db.query(Token).filter(Token.is_active == True).all()
        
        signals_created = []
        for token in tokens:
            # 当前周期交易数
            current_tx_count = db.query(func.count(Transaction.id)).filter(
                Transaction.token_id == token.id,
                Transaction.timestamp.between(period_start, period_end)
            ).scalar() or 0
            
            # 上一周期交易数
            prev_tx_count = db.query(func.count(Transaction.id)).filter(
                Transaction.token_id == token.id,
                Transaction.timestamp.between(prev_period_start, prev_period_end)
            ).scalar() or 0
            
            # 当前周期活跃地址数
            current_address_count = db.query(func.count(func.distinct(Transaction.from_address))).filter(
                Transaction.token_id == token.id,
                Transaction.timestamp.between(period_start, period_end)
            ).scalar() or 0
            
            # 上一周期活跃地址数
            prev_address_count = db.query(func.count(func.distinct(Transaction.from_address))).filter(
                Transaction.token_id == token.id,
                Transaction.timestamp.between(prev_period_start, prev_period_end)
            ).scalar() or 0
            
            # 计算增长率
            tx_growth = (current_tx_count - prev_tx_count) / max(1, prev_tx_count)
            address_growth = (current_address_count - prev_address_count) / max(1, prev_address_count)
            
            # 检查是否满足条件
            if tx_growth >= min_tx_growth and address_growth >= min_address_growth:
                # 计算信号强度和置信度
                strength = min(1.0, (tx_growth + address_growth) / 2)
                confidence = min(1.0, current_tx_count / 1000)
                
                # 创建信号
                signal = Signal(
                    strategy_id=strategy.id,
                    token_id=token.id,
                    signal_type="buy",
                    strength=strength,
                    confidence=confidence,
                    timestamp=now,
                    expiration=now + timedelta(days=7),
                    metadata={
                        "tx_growth": tx_growth,
                        "address_growth": address_growth,
                        "current_tx_count": current_tx_count,
                        "prev_tx_count": prev_tx_count,
                        "current_address_count": current_address_count,
                        "prev_address_count": prev_address_count
                    }
                )
                db.add(signal)
                signals_created.append({
                    "token_id": token.id,
                    "token_symbol": token.symbol,
                    "tx_growth": tx_growth,
                    "address_growth": address_growth,
                    "strength": strength,
                    "confidence": confidence
                })
        
        db.commit()
        return {
            "status": "success",
            "strategy": strategy.name,
            "signals_created": len(signals_created),
            "signals": signals_created
        }
    except Exception as e:
        db.rollback()
        logger.error(f"分析链上活动失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.analysis.analyze_smart_contract_interaction")
def analyze_smart_contract_interaction(strategy_id: int) -> Dict[str, Any]:
    """分析智能合约交互策略"""
    return {"status": "not_implemented", "message": "智能合约交互分析尚未实现"}


@celery_app.task(name="app.tasks.analysis.analyze_cross_chain_flow")
def analyze_cross_chain_flow(strategy_id: int) -> Dict[str, Any]:
    """分析跨链资金流动策略"""
    return {"status": "not_implemented", "message": "跨链资金流动分析尚未实现"}


@celery_app.task(name="app.tasks.analysis.run_custom_strategy")
def run_custom_strategy(strategy_id: int) -> Dict[str, Any]:
    """运行自定义策略"""
    return {"status": "not_implemented", "message": "自定义策略尚未实现"}
