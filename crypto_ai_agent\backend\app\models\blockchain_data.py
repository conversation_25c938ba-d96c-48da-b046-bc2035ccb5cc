from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, JSON, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class Blockchain(Base):
    """区块链网络表"""
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    symbol = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    api_endpoint = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    tokens = relationship("Token", back_populates="blockchain")
    blocks = relationship("Block", back_populates="blockchain")


class Token(Base):
    """加密货币代币表"""
    id = Column(Integer, primary_key=True, index=True)
    blockchain_id = Column(Integer, ForeignKey("blockchain.id"), nullable=False)
    name = Column(String, nullable=False)
    symbol = Column(String, nullable=False)
    contract_address = Column(String, index=True)
    decimals = Column(Integer, default=18)
    is_active = Column(Boolean, default=True)
    market_cap_rank = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    blockchain = relationship("Blockchain", back_populates="tokens")
    prices = relationship("TokenPrice", back_populates="token")
    transactions = relationship("Transaction", back_populates="token")
    wallets = relationship("WalletToken", back_populates="token")


class TokenPrice(Base):
    """代币价格历史表"""
    id = Column(Integer, primary_key=True, index=True)
    token_id = Column(Integer, ForeignKey("token.id"), nullable=False)
    price_usd = Column(Float, nullable=False)
    market_cap_usd = Column(Float)
    volume_24h_usd = Column(Float)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    token = relationship("Token", back_populates="prices")


class Block(Base):
    """区块数据表"""
    id = Column(Integer, primary_key=True, index=True)
    blockchain_id = Column(Integer, ForeignKey("blockchain.id"), nullable=False)
    block_number = Column(Integer, nullable=False, index=True)
    block_hash = Column(String, nullable=False, unique=True, index=True)
    parent_hash = Column(String)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    transaction_count = Column(Integer)
    gas_used = Column(Integer)
    gas_limit = Column(Integer)
    base_fee_per_gas = Column(Integer)
    extra_data = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    blockchain = relationship("Blockchain", back_populates="blocks")
    transactions = relationship("Transaction", back_populates="block")


class Transaction(Base):
    """交易数据表"""
    id = Column(Integer, primary_key=True, index=True)
    blockchain_id = Column(Integer, ForeignKey("blockchain.id"), nullable=False)
    block_id = Column(Integer, ForeignKey("block.id"), nullable=False)
    token_id = Column(Integer, ForeignKey("token.id"))
    tx_hash = Column(String, nullable=False, unique=True, index=True)
    from_address = Column(String, nullable=False, index=True)
    to_address = Column(String, index=True)
    value = Column(String)  # 使用字符串存储大整数
    gas = Column(Integer)
    gas_price = Column(Integer)
    input_data = Column(Text)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    status = Column(Boolean)  # 交易是否成功
    transaction_type = Column(String)  # 交易类型: transfer, swap, liquidity, etc.
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    blockchain = relationship("Blockchain")
    block = relationship("Block", back_populates="transactions")
    token = relationship("Token", back_populates="transactions")


class Wallet(Base):
    """钱包地址表"""
    id = Column(Integer, primary_key=True, index=True)
    address = Column(String, nullable=False, unique=True, index=True)
    first_seen = Column(DateTime(timezone=True))
    last_active = Column(DateTime(timezone=True))
    is_contract = Column(Boolean, default=False)
    is_exchange = Column(Boolean, default=False)
    is_whale = Column(Boolean, default=False)
    label = Column(String)  # 例如: "Binance", "Whale", "Developer"
    tags = Column(JSON)  # 标签列表
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    tokens = relationship("WalletToken", back_populates="wallet")


class WalletToken(Base):
    """钱包持有代币表"""
    id = Column(Integer, primary_key=True, index=True)
    wallet_id = Column(Integer, ForeignKey("wallet.id"), nullable=False)
    token_id = Column(Integer, ForeignKey("token.id"), nullable=False)
    balance = Column(String)  # 使用字符串存储大整数
    last_updated = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    wallet = relationship("Wallet", back_populates="tokens")
    token = relationship("Token", back_populates="wallets")
