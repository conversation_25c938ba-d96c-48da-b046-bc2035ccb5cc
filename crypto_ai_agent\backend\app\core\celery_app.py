from celery import Celery
from app.core.config import settings

celery_app = Celery(
    "worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=["app.tasks.blockchain_data", "app.tasks.analysis", "app.tasks.reporting"]
)

# 配置Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_max_tasks_per_child=1000,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_track_started=True,
    worker_prefetch_multiplier=4,
    task_routes={
        "app.tasks.blockchain_data.*": {"queue": "blockchain_data"},
        "app.tasks.analysis.*": {"queue": "analysis"},
        "app.tasks.reporting.*": {"queue": "reporting"},
    },
    beat_schedule={
        "fetch-blockchain-data-every-hour": {
            "task": "app.tasks.blockchain_data.fetch_all_blockchain_data",
            "schedule": 3600.0,  # 每小时
            "options": {"queue": "blockchain_data"},
        },
        "analyze-data-daily": {
            "task": "app.tasks.analysis.run_daily_analysis",
            "schedule": 86400.0,  # 每天
            "options": {"queue": "analysis"},
        },
        "generate-daily-report": {
            "task": "app.tasks.reporting.generate_daily_report",
            "schedule": 86400.0,  # 每天
            "options": {"queue": "reporting"},
        },
    },
)

if __name__ == "__main__":
    celery_app.start()
