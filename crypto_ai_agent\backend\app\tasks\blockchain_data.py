import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time

from web3 import Web3
from sqlalchemy.orm import Session
import httpx

from app.core.celery_app import celery_app
from app.core.config import settings
from app.db.session import SessionLocal
from app.models.blockchain_data import Blockchain, Token, Block, Transaction, Wallet, WalletToken, TokenPrice
from app.services.blockchain import get_web3_provider

logger = logging.getLogger(__name__)


@celery_app.task(name="app.tasks.blockchain_data.fetch_all_blockchain_data")
def fetch_all_blockchain_data() -> Dict[str, Any]:
    """获取所有支持的区块链数据"""
    results = {}
    
    for chain in settings.SUPPORTED_CHAINS:
        try:
            result = fetch_blockchain_data.delay(chain)
            results[chain] = {"task_id": result.id, "status": "started"}
        except Exception as e:
            logger.error(f"获取{chain}数据失败: {e}")
            results[chain] = {"status": "failed", "error": str(e)}
    
    return results


@celery_app.task(name="app.tasks.blockchain_data.fetch_blockchain_data")
def fetch_blockchain_data(blockchain_name: str) -> Dict[str, Any]:
    """获取特定区块链的数据"""
    db = SessionLocal()
    try:
        # 获取区块链信息
        blockchain = db.query(Blockchain).filter(Blockchain.name == blockchain_name).first()
        if not blockchain:
            logger.error(f"区块链{blockchain_name}不存在")
            return {"status": "failed", "error": f"区块链{blockchain_name}不存在"}
        
        # 获取最新区块
        latest_block = fetch_latest_blocks.delay(blockchain.id, 10)
        
        # 获取热门代币价格
        token_prices = fetch_token_prices.delay(blockchain.id)
        
        # 获取鲸鱼钱包活动
        whale_activity = fetch_whale_activity.delay(blockchain.id)
        
        return {
            "status": "success",
            "blockchain": blockchain_name,
            "tasks": {
                "latest_blocks": latest_block.id,
                "token_prices": token_prices.id,
                "whale_activity": whale_activity.id
            }
        }
    except Exception as e:
        logger.error(f"获取{blockchain_name}数据失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.blockchain_data.fetch_latest_blocks")
def fetch_latest_blocks(blockchain_id: int, block_count: int = 10) -> Dict[str, Any]:
    """获取最新区块数据"""
    db = SessionLocal()
    try:
        blockchain = db.query(Blockchain).filter(Blockchain.id == blockchain_id).first()
        if not blockchain:
            return {"status": "failed", "error": "区块链不存在"}
        
        # 获取Web3提供者
        w3 = get_web3_provider(blockchain.name)
        if not w3:
            return {"status": "failed", "error": "无法连接到区块链节点"}
        
        # 获取最新区块号
        latest_block_number = w3.eth.block_number
        
        # 获取最近的区块
        blocks_data = []
        for i in range(block_count):
            block_number = latest_block_number - i
            if block_number < 0:
                break
                
            # 检查区块是否已存在
            existing_block = db.query(Block).filter(
                Block.blockchain_id == blockchain_id,
                Block.block_number == block_number
            ).first()
            
            if existing_block:
                continue
                
            # 获取区块数据
            block_data = w3.eth.get_block(block_number, full_transactions=True)
            
            # 创建区块记录
            block = Block(
                blockchain_id=blockchain_id,
                block_number=block_number,
                block_hash=block_data.hash.hex(),
                parent_hash=block_data.parentHash.hex(),
                timestamp=datetime.fromtimestamp(block_data.timestamp),
                transaction_count=len(block_data.transactions),
                gas_used=block_data.gasUsed,
                gas_limit=block_data.gasLimit,
                base_fee_per_gas=getattr(block_data, "baseFeePerGas", 0),
                extra_data={}
            )
            db.add(block)
            db.flush()
            
            # 处理交易
            process_transactions.delay(blockchain_id, block.id, [tx.hash.hex() for tx in block_data.transactions])
            
            blocks_data.append({
                "block_number": block_number,
                "block_hash": block_data.hash.hex(),
                "transaction_count": len(block_data.transactions)
            })
        
        db.commit()
        return {"status": "success", "blocks": blocks_data}
    except Exception as e:
        db.rollback()
        logger.error(f"获取最新区块失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.blockchain_data.process_transactions")
def process_transactions(blockchain_id: int, block_id: int, tx_hashes: List[str]) -> Dict[str, Any]:
    """处理区块中的交易"""
    db = SessionLocal()
    try:
        blockchain = db.query(Blockchain).filter(Blockchain.id == blockchain_id).first()
        if not blockchain:
            return {"status": "failed", "error": "区块链不存在"}
        
        block = db.query(Block).filter(Block.id == block_id).first()
        if not block:
            return {"status": "failed", "error": "区块不存在"}
        
        # 获取Web3提供者
        w3 = get_web3_provider(blockchain.name)
        if not w3:
            return {"status": "failed", "error": "无法连接到区块链节点"}
        
        processed_txs = []
        for tx_hash in tx_hashes:
            # 检查交易是否已存在
            existing_tx = db.query(Transaction).filter(Transaction.tx_hash == tx_hash).first()
            if existing_tx:
                continue
                
            # 获取交易数据
            tx_data = w3.eth.get_transaction(tx_hash)
            tx_receipt = w3.eth.get_transaction_receipt(tx_hash)
            
            # 确定代币ID
            token_id = None
            if tx_data.to:
                token = db.query(Token).filter(
                    Token.blockchain_id == blockchain_id,
                    Token.contract_address == tx_data.to.lower()
                ).first()
                if token:
                    token_id = token.id
            
            # 创建交易记录
            transaction = Transaction(
                blockchain_id=blockchain_id,
                block_id=block_id,
                token_id=token_id,
                tx_hash=tx_hash,
                from_address=tx_data["from"].lower(),
                to_address=tx_data.to.lower() if tx_data.to else None,
                value=str(tx_data.value),
                gas=tx_data.gas,
                gas_price=tx_data.gasPrice,
                input_data=tx_data.input,
                timestamp=block.timestamp,
                status=tx_receipt.status == 1,
                transaction_type=determine_transaction_type(tx_data)
            )
            db.add(transaction)
            
            # 处理钱包地址
            process_wallet_address.delay(tx_data["from"].lower(), block.timestamp)
            if tx_data.to:
                process_wallet_address.delay(tx_data.to.lower(), block.timestamp)
            
            processed_txs.append(tx_hash)
        
        db.commit()
        return {"status": "success", "processed_transactions": len(processed_txs)}
    except Exception as e:
        db.rollback()
        logger.error(f"处理交易失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.blockchain_data.process_wallet_address")
def process_wallet_address(address: str, timestamp: datetime) -> Dict[str, Any]:
    """处理钱包地址"""
    db = SessionLocal()
    try:
        # 检查钱包是否已存在
        wallet = db.query(Wallet).filter(Wallet.address == address).first()
        
        if wallet:
            # 更新最后活跃时间
            wallet.last_active = timestamp
        else:
            # 创建新钱包记录
            wallet = Wallet(
                address=address,
                first_seen=timestamp,
                last_active=timestamp,
                is_contract=is_contract_address(address),
                is_exchange=is_exchange_address(address),
                is_whale=False,  # 默认不是鲸鱼，后续分析会更新
                label=None,
                tags=[]
            )
            db.add(wallet)
        
        db.commit()
        return {"status": "success", "wallet_id": wallet.id}
    except Exception as e:
        db.rollback()
        logger.error(f"处理钱包地址失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.blockchain_data.fetch_token_prices")
def fetch_token_prices(blockchain_id: int) -> Dict[str, Any]:
    """获取代币价格数据"""
    db = SessionLocal()
    try:
        # 获取区块链上的所有代币
        tokens = db.query(Token).filter(Token.blockchain_id == blockchain_id, Token.is_active == True).all()
        
        if not tokens:
            return {"status": "success", "message": "没有活跃的代币"}
        
        # 使用CoinGecko API获取价格
        token_ids = [token.symbol.lower() for token in tokens]
        
        # 构建API请求
        url = "https://api.coingecko.com/api/v3/simple/price"
        params = {
            "ids": ",".join(token_ids),
            "vs_currencies": "usd",
            "include_market_cap": "true",
            "include_24hr_vol": "true",
            "include_last_updated_at": "true"
        }
        
        # 添加API密钥（如果有）
        if settings.COINGECKO_API_KEY:
            params["x_cg_pro_api_key"] = settings.COINGECKO_API_KEY
        
        # 发送请求
        response = httpx.get(url, params=params)
        response.raise_for_status()
        price_data = response.json()
        
        # 处理价格数据
        timestamp = datetime.now()
        for token in tokens:
            token_id_lower = token.symbol.lower()
            if token_id_lower in price_data:
                data = price_data[token_id_lower]
                
                # 创建价格记录
                token_price = TokenPrice(
                    token_id=token.id,
                    price_usd=data.get("usd", 0),
                    market_cap_usd=data.get("usd_market_cap", 0),
                    volume_24h_usd=data.get("usd_24h_vol", 0),
                    timestamp=timestamp
                )
                db.add(token_price)
        
        db.commit()
        return {"status": "success", "tokens_updated": len(tokens)}
    except Exception as e:
        db.rollback()
        logger.error(f"获取代币价格失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.blockchain_data.fetch_whale_activity")
def fetch_whale_activity(blockchain_id: int) -> Dict[str, Any]:
    """获取鲸鱼钱包活动数据"""
    db = SessionLocal()
    try:
        # 获取鲸鱼钱包
        whale_wallets = db.query(Wallet).filter(Wallet.is_whale == True).all()
        
        if not whale_wallets:
            return {"status": "success", "message": "没有鲸鱼钱包"}
        
        # 获取最近24小时的交易
        yesterday = datetime.now() - timedelta(days=1)
        
        whale_addresses = [wallet.address for wallet in whale_wallets]
        transactions = db.query(Transaction).filter(
            Transaction.blockchain_id == blockchain_id,
            Transaction.timestamp >= yesterday,
            Transaction.from_address.in_(whale_addresses) | Transaction.to_address.in_(whale_addresses)
        ).all()
        
        return {
            "status": "success", 
            "whale_count": len(whale_wallets),
            "transaction_count": len(transactions)
        }
    except Exception as e:
        logger.error(f"获取鲸鱼活动失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


# 辅助函数
def determine_transaction_type(tx_data: Dict[str, Any]) -> str:
    """确定交易类型"""
    if not tx_data.to:
        return "contract_creation"
    
    if tx_data.value > 0 and not tx_data.input or tx_data.input == "0x":
        return "transfer"
    
    # 简单的方法识别常见交易类型
    input_data = tx_data.input.lower()
    if input_data.startswith("0xa9059cbb"):  # ERC20 transfer
        return "erc20_transfer"
    elif input_data.startswith("0x23b872dd"):  # ERC20 transferFrom
        return "erc20_transfer_from"
    elif input_data.startswith("0x095ea7b3"):  # ERC20 approve
        return "erc20_approve"
    elif input_data.startswith("0x7ff36ab5"):  # Uniswap swapExactETHForTokens
        return "swap"
    
    return "contract_interaction"


def is_contract_address(address: str) -> bool:
    """检查地址是否为合约地址"""
    # 实际实现需要查询区块链
    # 这里简化处理
    return False


def is_exchange_address(address: str) -> bool:
    """检查地址是否为交易所地址"""
    # 实际实现需要查询已知交易所地址列表
    # 这里简化处理
    return False
