import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Grid, Paper, Typography, Box, CircularProgress, Card, CardContent, Chip } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import WarningIcon from '@mui/icons-material/Warning';
import { Link } from 'react-router-dom';

// 组件
import TokenPriceChart from '../components/charts/TokenPriceChart';
import SignalsList from '../components/SignalsList';
import TopTokensTable from '../components/TopTokensTable';
import WhaleActivityChart from '../components/charts/WhaleActivityChart';

// API
import { fetchDashboardData, fetchLatestSignals, fetchTopTokens } from '../api/dashboardApi';

const Dashboard = () => {
  const [timeRange, setTimeRange] = useState('24h');

  // 获取仪表盘数据
  const { data: dashboardData, isLoading: isDashboardLoading, error: dashboardError } = useQuery(
    ['dashboardData', timeRange],
    () => fetchDashboardData(timeRange),
    {
      refetchInterval: 300000, // 5分钟刷新一次
    }
  );

  // 获取最新信号
  const { data: latestSignals, isLoading: isSignalsLoading } = useQuery(
    ['latestSignals'],
    fetchLatestSignals,
    {
      refetchInterval: 300000, // 5分钟刷新一次
    }
  );

  // 获取热门代币
  const { data: topTokens, isLoading: isTopTokensLoading } = useQuery(
    ['topTokens'],
    fetchTopTokens,
    {
      refetchInterval: 300000, // 5分钟刷新一次
    }
  );

  // 处理时间范围变化
  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
  };

  if (isDashboardLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (dashboardError) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <Typography color="error" variant="h6">
          <WarningIcon /> 加载数据时出错
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Typography variant="h4" gutterBottom>
        仪表盘
      </Typography>

      {/* 概览卡片 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                监测的代币
              </Typography>
              <Typography variant="h5" component="div">
                {dashboardData?.tokenCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                活跃策略
              </Typography>
              <Typography variant="h5" component="div">
                {dashboardData?.activeStrategies || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                今日信号
              </Typography>
              <Typography variant="h5" component="div">
                {dashboardData?.todaySignals || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                监测的钱包
              </Typography>
              <Typography variant="h5" component="div">
                {dashboardData?.walletCount || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 主要内容 */}
      <Grid container spacing={3}>
        {/* 价格图表 */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              热门代币价格趋势
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Chip
                label="24小时"
                onClick={() => handleTimeRangeChange('24h')}
                color={timeRange === '24h' ? 'primary' : 'default'}
              />
              <Chip
                label="7天"
                onClick={() => handleTimeRangeChange('7d')}
                color={timeRange === '7d' ? 'primary' : 'default'}
              />
              <Chip
                label="30天"
                onClick={() => handleTimeRangeChange('30d')}
                color={timeRange === '30d' ? 'primary' : 'default'}
              />
            </Box>
            <TokenPriceChart data={dashboardData?.priceData || []} timeRange={timeRange} />
          </Paper>
        </Grid>

        {/* 最新信号 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              最新投资信号
            </Typography>
            {isSignalsLoading ? (
              <CircularProgress />
            ) : (
              <SignalsList signals={latestSignals || []} />
            )}
            <Box sx={{ mt: 2, textAlign: 'right' }}>
              <Link to="/strategies" style={{ textDecoration: 'none' }}>
                <Typography color="primary" variant="body2">
                  查看所有策略 →
                </Typography>
              </Link>
            </Box>
          </Paper>
        </Grid>

        {/* 热门代币表格 */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              热门代币
            </Typography>
            {isTopTokensLoading ? (
              <CircularProgress />
            ) : (
              <TopTokensTable tokens={topTokens || []} />
            )}
          </Paper>
        </Grid>

        {/* 鲸鱼活动图表 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              鲸鱼活动
            </Typography>
            <WhaleActivityChart data={dashboardData?.whaleActivity || []} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
