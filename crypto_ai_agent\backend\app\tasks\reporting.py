import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import os

from app.core.celery_app import celery_app
from app.core.config import settings
from app.db.session import SessionLocal
from app.models.blockchain_data import Token, TokenPrice
from app.models.analysis import Signal, Strategy

logger = logging.getLogger(__name__)


@celery_app.task(name="app.tasks.reporting.generate_daily_report")
def generate_daily_report() -> Dict[str, Any]:
    """生成每日投资报告"""
    db = SessionLocal()
    try:
        # 获取当前日期
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        # 获取昨日生成的信号
        signals = db.query(Signal).filter(
            Signal.timestamp >= yesterday,
            Signal.timestamp < today
        ).all()
        
        if not signals:
            return {"status": "success", "message": "昨日没有生成新的信号"}
        
        # 按策略分组信号
        signals_by_strategy = {}
        for signal in signals:
            strategy_name = db.query(Strategy.name).filter(Strategy.id == signal.strategy_id).scalar()
            if strategy_name not in signals_by_strategy:
                signals_by_strategy[strategy_name] = []
            
            token = db.query(Token).filter(Token.id == signal.token_id).first()
            
            signals_by_strategy[strategy_name].append({
                "token_symbol": token.symbol,
                "token_name": token.name,
                "signal_type": signal.signal_type,
                "strength": signal.strength,
                "confidence": signal.confidence,
                "timestamp": signal.timestamp.isoformat(),
                "expiration": signal.expiration.isoformat() if signal.expiration else None,
                "metadata": signal.metadata
            })
        
        # 获取价格变动最大的代币
        price_changes = get_top_price_changes(db)
        
        # 生成报告
        report = {
            "date": today.isoformat(),
            "signals_count": len(signals),
            "signals_by_strategy": signals_by_strategy,
            "top_price_gainers": price_changes["gainers"],
            "top_price_losers": price_changes["losers"]
        }
        
        # 保存报告
        save_report(report)
        
        return {
            "status": "success",
            "report_date": today.isoformat(),
            "signals_count": len(signals)
        }
    except Exception as e:
        logger.error(f"生成每日报告失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task(name="app.tasks.reporting.generate_weekly_report")
def generate_weekly_report() -> Dict[str, Any]:
    """生成每周投资报告"""
    db = SessionLocal()
    try:
        # 获取当前日期
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        
        # 获取过去一周生成的信号
        signals = db.query(Signal).filter(
            Signal.timestamp >= week_ago,
            Signal.timestamp < today
        ).all()
        
        if not signals:
            return {"status": "success", "message": "过去一周没有生成新的信号"}
        
        # 按策略分组信号
        signals_by_strategy = {}
        for signal in signals:
            strategy_name = db.query(Strategy.name).filter(Strategy.id == signal.strategy_id).scalar()
            if strategy_name not in signals_by_strategy:
                signals_by_strategy[strategy_name] = []
            
            token = db.query(Token).filter(Token.id == signal.token_id).first()
            
            signals_by_strategy[strategy_name].append({
                "token_symbol": token.symbol,
                "token_name": token.name,
                "signal_type": signal.signal_type,
                "strength": signal.strength,
                "confidence": signal.confidence,
                "timestamp": signal.timestamp.isoformat(),
                "expiration": signal.expiration.isoformat() if signal.expiration else None,
                "metadata": signal.metadata
            })
        
        # 获取价格变动最大的代币
        price_changes = get_top_price_changes(db, days=7)
        
        # 生成报告
        report = {
            "date": today.isoformat(),
            "period": "weekly",
            "start_date": week_ago.isoformat(),
            "end_date": today.isoformat(),
            "signals_count": len(signals),
            "signals_by_strategy": signals_by_strategy,
            "top_price_gainers": price_changes["gainers"],
            "top_price_losers": price_changes["losers"]
        }
        
        # 保存报告
        save_report(report, report_type="weekly")
        
        return {
            "status": "success",
            "report_date": today.isoformat(),
            "signals_count": len(signals)
        }
    except Exception as e:
        logger.error(f"生成每周报告失败: {e}")
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


def get_top_price_changes(db: Session, days: int = 1, limit: int = 10) -> Dict[str, List[Dict[str, Any]]]:
    """获取价格变动最大的代币"""
    today = datetime.now().date()
    yesterday = today - timedelta(days=days)
    
    # 获取今日价格
    today_prices = db.query(
        TokenPrice.token_id,
        TokenPrice.price_usd
    ).filter(
        TokenPrice.timestamp >= today
    ).order_by(
        TokenPrice.timestamp.desc()
    ).distinct(
        TokenPrice.token_id
    ).all()
    
    # 获取昨日价格
    yesterday_prices = db.query(
        TokenPrice.token_id,
        TokenPrice.price_usd
    ).filter(
        TokenPrice.timestamp >= yesterday,
        TokenPrice.timestamp < today
    ).order_by(
        TokenPrice.timestamp.desc()
    ).distinct(
        TokenPrice.token_id
    ).all()
    
    # 计算价格变动
    price_changes = []
    for today_price in today_prices:
        token_id = today_price.token_id
        today_price_usd = today_price.price_usd
        
        # 查找昨日价格
        yesterday_price_usd = None
        for yp in yesterday_prices:
            if yp.token_id == token_id:
                yesterday_price_usd = yp.price_usd
                break
        
        if yesterday_price_usd is None or yesterday_price_usd == 0:
            continue
        
        # 计算价格变动百分比
        price_change_pct = (today_price_usd - yesterday_price_usd) / yesterday_price_usd * 100
        
        # 获取代币信息
        token = db.query(Token).filter(Token.id == token_id).first()
        if not token:
            continue
        
        price_changes.append({
            "token_id": token_id,
            "token_symbol": token.symbol,
            "token_name": token.name,
            "price_change_pct": price_change_pct,
            "current_price_usd": today_price_usd,
            "previous_price_usd": yesterday_price_usd
        })
    
    # 按价格变动排序
    price_changes.sort(key=lambda x: x["price_change_pct"], reverse=True)
    
    # 分离涨幅和跌幅最大的代币
    gainers = [pc for pc in price_changes if pc["price_change_pct"] > 0][:limit]
    losers = sorted([pc for pc in price_changes if pc["price_change_pct"] < 0], key=lambda x: x["price_change_pct"])[:limit]
    
    return {
        "gainers": gainers,
        "losers": losers
    }


def save_report(report: Dict[str, Any], report_type: str = "daily") -> None:
    """保存报告到文件"""
    # 创建报告目录
    reports_dir = os.path.join(os.getcwd(), "reports")
    if not os.path.exists(reports_dir):
        os.makedirs(reports_dir)
    
    # 生成文件名
    date_str = report["date"]
    filename = f"{report_type}_report_{date_str}.json"
    file_path = os.path.join(reports_dir, filename)
    
    # 保存报告
    with open(file_path, "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"报告已保存到: {file_path}")
