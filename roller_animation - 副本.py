import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from matplotlib.widgets import Button, Slider

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']  # 优先使用的中文字体列表
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 设置参数
length_ratio = 13  # 辊筒的长径比
left_diameter = 1.0  # 左侧辊筒直径（单位）
right_diameter = 0.8 * left_diameter  # 右侧辊筒直径略小于左侧
left_length = length_ratio * left_diameter  # 左侧辊筒长度
right_length = left_length  # 右侧辊筒长度与左侧相同

# 创建3D图形和坐标轴，调整图形大小以容纳控制按钮
fig = plt.figure(figsize=(14, 10))
ax = fig.add_subplot(111, projection='3d')

# 设置初始视角
initial_elev = 76
initial_azim = -45
initial_roll = 132  # 添加初始旋转角度
initial_zoom = 1.0  # 添加初始缩放比例

# 保存当前视角参数和缩放状态
view_params = {
    'elev': initial_elev,
    'azim': initial_azim,
    'roll': initial_roll,  # 添加旋转角度参数
    'zoom': initial_zoom,  # 添加缩放参数
    'xlim': (-2, 2),
    'ylim': (-2, 2),
    'zlim': (left_length/2+1, -left_length/2-1)  # 反转Z轴方向
}

# 设置初始坐标轴范围 - 注意这里交换了X和Z轴的范围，并反转了Z轴
ax.set_xlim(view_params['xlim'])
ax.set_ylim(view_params['ylim'])
ax.set_zlim(view_params['zlim'])
ax.set_title('辊筒动画场景 - 3D视图')

# 显示坐标轴
ax.set_xlabel('X轴')
ax.set_ylabel('Y轴')
ax.set_zlabel('Z轴 (长度)')

# 设置等比例显示，确保圆形不变形
try:
    ax.set_box_aspect([4, 4, left_length+2])  # 设置坐标轴比例，注意这里交换了X和Z轴的比例
except AttributeError:
    ax.set_aspect('equal')

# 定义更接近真实材料的颜色
steel_color = '#A9A9A9'  # 钢材料的银灰色
rubber_color = '#1A1A1A'  # 黑色橡胶的深黑色

# 计算辊筒位置，使它们靠在一起 - 现在是在Y轴方向上靠近
left_center_x = 0
left_center_y = left_diameter/2
left_center_z = 0
right_center_x = 0
right_center_y = -right_diameter/2
right_center_z = 0

# 创建圆柱体函数 - 添加标记线，轴向为Z轴（反向）
def create_cylinder_with_line(center_x, center_y, center_z, radius, length, color):
    # 圆柱体参数 - 注意这里交换了轴向，现在沿着Z轴延伸（反向）
    u = np.linspace(0, 2 * np.pi, 30)
    h = np.linspace(-length/2, length/2, 10)
    x = np.outer(np.cos(u), np.ones_like(h)) * radius + center_x
    y = np.outer(np.sin(u), np.ones_like(h)) * radius + center_y
    z = np.outer(np.ones_like(u), -h) + center_z  # 注意这里的负号，反转Z轴方向
    
    # 绘制圆柱体表面
    cylinder = ax.plot_surface(x, y, z, color=color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面 - 使用参数方程确保是圆形
    theta = np.linspace(0, 2 * np.pi, 30)
    r = np.linspace(0, radius, 10)
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = center_x + r_grid * np.cos(theta_grid)
    y_disk = center_y + r_grid * np.sin(theta_grid)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (center_z + length/2)  # 反转后的底面
    bottom = ax.plot_surface(x_disk, y_disk, z_bottom, color=color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (center_z - length/2)  # 反转后的顶面
    top = ax.plot_surface(x_disk, y_disk, z_top, color=color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    line_x = [center_x + radius, center_x + radius]
    line_y = [center_y, center_y]
    line_z = [center_z + length/2, center_z - length/2]  # 反转Z轴方向
    line, = ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    return cylinder, bottom, top, line

# 初始创建两个辊筒
left_cylinder, left_bottom, left_top, left_line = create_cylinder_with_line(
    left_center_x, left_center_y, left_center_z, left_diameter/2, left_length, steel_color)
right_cylinder, right_bottom, right_top, right_line = create_cylinder_with_line(
    right_center_x, right_center_y, right_center_z, right_diameter/2, right_length, rubber_color)

# 添加文字说明
ax.text(0, left_center_y, left_length/4, f'钢材料辊筒\n长径比={length_ratio}\n顺时针旋转', fontsize=9)
ax.text(0, right_center_y, right_length/4, f'橡胶材料辊筒\n长径比={length_ratio}\n逆时针旋转', fontsize=9)

# 添加接触点
contact_point, = ax.plot([0], [0], [0], 'ro', markersize=8)

# 添加旋转方向指示箭头
arrow_length = 0.5
# 左侧辊筒箭头（顺时针）
ax.quiver(left_center_x, left_center_y+left_diameter/2, 0, 0, -arrow_length, 0, color='blue', arrow_length_ratio=0.3)
# 右侧辊筒箭头（逆时针）
ax.quiver(right_center_x, right_center_y-right_diameter/2, 0, 0, arrow_length, 0, color='blue', arrow_length_ratio=0.3)

# 添加坐标轴原点标记
ax.scatter([0], [0], [0], color='red', s=50, marker='o')
ax.text(0.2, 0.2, 0.2, "原点(0,0,0)", color='red', fontsize=8)

# 调整图形布局，为控制按钮留出空间
plt.subplots_adjust(bottom=0.4)  # 增加底部空间以容纳新的滑块

# 添加视角控制滑块
ax_elev = plt.axes([0.25, 0.25, 0.65, 0.03])
ax_azim = plt.axes([0.25, 0.20, 0.65, 0.03])
ax_roll = plt.axes([0.25, 0.15, 0.65, 0.03])
ax_zoom = plt.axes([0.25, 0.10, 0.65, 0.03])  # 添加缩放滑块

elev_slider = Slider(ax_elev, '仰角', 0, 180, valinit=initial_elev)
azim_slider = Slider(ax_azim, '方位角', -180, 180, valinit=initial_azim)
roll_slider = Slider(ax_roll, '旋转角度', -180, 180, valinit=initial_roll)
zoom_slider = Slider(ax_zoom, '缩放', 0.1, 3.0, valinit=initial_zoom)  # 添加缩放滑块

# 添加预设视角按钮
ax_front = plt.axes([0.25, 0.05, 0.15, 0.03])
ax_top = plt.axes([0.45, 0.05, 0.15, 0.03])
ax_side = plt.axes([0.65, 0.05, 0.15, 0.03])
ax_reset = plt.axes([0.85, 0.05, 0.1, 0.03])

btn_front = Button(ax_front, '正视图')
btn_top = Button(ax_top, '俯视图')
btn_side = Button(ax_side, '侧视图')
btn_reset = Button(ax_reset, '重置')

# 滑块回调函数
def update_elev(val):
    view_params['elev'] = val
    ax.view_init(elev=val, azim=view_params['azim'])
    fig.canvas.draw_idle()

def update_azim(val):
    view_params['azim'] = val
    ax.view_init(elev=view_params['elev'], azim=val)
    fig.canvas.draw_idle()

def update_roll(val):
    view_params['roll'] = val
    # 在matplotlib 3.1+版本中支持roll参数
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=val)
    except TypeError:
        # 对于不支持roll参数的旧版本，使用变通方法
        print("您的Matplotlib版本不支持roll参数，请升级到3.1+版本以使用此功能")
    fig.canvas.draw_idle()

def update_zoom(val):
    view_params['zoom'] = val
    # 根据缩放比例调整坐标轴范围
    base_xlim = (-2, 2)
    base_ylim = (-2, 2)
    base_zlim = (left_length/2+1, -left_length/2-1)
    
    # 计算新的范围
    center_x = sum(base_xlim) / 2
    center_y = sum(base_ylim) / 2
    center_z = sum(base_zlim) / 2
    
    half_width_x = (base_xlim[1] - base_xlim[0]) / 2 / val
    half_width_y = (base_ylim[1] - base_ylim[0]) / 2 / val
    half_width_z = (base_zlim[0] - base_zlim[1]) / 2 / val  # 注意Z轴是反向的
    
    new_xlim = (center_x - half_width_x, center_x + half_width_x)
    new_ylim = (center_y - half_width_y, center_y + half_width_y)
    new_zlim = (center_z + half_width_z, center_z - half_width_z)  # 注意Z轴是反向的
    
    view_params['xlim'] = new_xlim
    view_params['ylim'] = new_ylim
    view_params['zlim'] = new_zlim
    
    ax.set_xlim(new_xlim)
    ax.set_ylim(new_ylim)
    ax.set_zlim(new_zlim)
    
    fig.canvas.draw_idle()

# 按钮回调函数
def view_front(event):
    view_params['elev'] = 0
    view_params['azim'] = 0
    view_params['roll'] = 0
    elev_slider.set_val(view_params['elev'])
    azim_slider.set_val(view_params['azim'])
    roll_slider.set_val(view_params['roll'])
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    fig.canvas.draw_idle()

def view_top(event):
    view_params['elev'] = 90
    view_params['azim'] = -90
    view_params['roll'] = 0
    elev_slider.set_val(view_params['elev'])
    azim_slider.set_val(view_params['azim'])
    roll_slider.set_val(view_params['roll'])
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    fig.canvas.draw_idle()

def view_side(event):
    view_params['elev'] = 0
    view_params['azim'] = -90
    view_params['roll'] = 0
    elev_slider.set_val(view_params['elev'])
    azim_slider.set_val(view_params['azim'])
    roll_slider.set_val(view_params['roll'])
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    fig.canvas.draw_idle()

def reset_view(event):
    view_params['elev'] = initial_elev
    view_params['azim'] = initial_azim
    view_params['roll'] = initial_roll
    view_params['zoom'] = initial_zoom
    elev_slider.set_val(view_params['elev'])
    azim_slider.set_val(view_params['azim'])
    roll_slider.set_val(view_params['roll'])
    zoom_slider.set_val(view_params['zoom'])
    
    # 重置缩放
    view_params['xlim'] = (-2, 2)
    view_params['ylim'] = (-2, 2)
    view_params['zlim'] = (left_length/2+1, -left_length/2-1)
    ax.set_xlim(view_params['xlim'])
    ax.set_ylim(view_params['ylim'])
    ax.set_zlim(view_params['zlim'])
    
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    fig.canvas.draw_idle()

# 注册回调函数
elev_slider.on_changed(update_elev)
azim_slider.on_changed(update_azim)
roll_slider.on_changed(update_roll)
zoom_slider.on_changed(update_zoom)  # 注册缩放滑块回调
btn_front.on_clicked(view_front)
btn_top.on_clicked(view_top)
btn_side.on_clicked(view_side)
btn_reset.on_clicked(reset_view)

# 禁用鼠标交互
ax.mouse_init(rotate_btn=None, zoom_btn=None)

# 动画更新函数 - 重新绘制整个辊筒
def update(frame):
    global view_params
    
    # 清除之前的辊筒
    ax.clear()
    
    # 显示坐标轴
    ax.set_xlabel('X轴')
    ax.set_ylabel('Y轴')
    ax.set_zlabel('Z轴 (长度)')
    
    # 重新设置坐标轴范围，使用保存的视图参数
    ax.set_xlim(view_params['xlim'])
    ax.set_ylim(view_params['ylim'])
    ax.set_zlim(view_params['zlim'])
    ax.set_title('辊筒动画场景 - 3D视图')
    
    # 使用更安全的方式设置比例
    try:
        ax.set_box_aspect([4, 4, left_length+2])
    except AttributeError:
        ax.set_aspect('equal')
    
    # 左侧辊筒顺时针旋转（角度为正值）
    left_angle = frame * 5  # 度数
    
    # 右侧辊筒逆时针旋转（角度为负值）
    right_angle = -frame * 5  # 度数
    
    # 创建旋转后的辊筒
    # 左侧辊筒 - 顺时针旋转
    left_rad = np.radians(left_angle)
    left_cylinder_x = left_center_x
    left_cylinder_y = left_center_y
    left_cylinder_z = left_center_z
    
    # 创建旋转后的左侧辊筒
    u = np.linspace(0, 2 * np.pi, 30)
    h = np.linspace(-left_length/2, left_length/2, 10)
    x = np.outer(np.cos(u + left_rad), np.ones_like(h)) * (left_diameter/2) + left_cylinder_x
    y = np.outer(np.sin(u + left_rad), np.ones_like(h)) * (left_diameter/2) + left_cylinder_y
    z = np.outer(np.ones_like(u), -h) + left_cylinder_z  # 注意这里的负号，反转Z轴方向
    
    ax.plot_surface(x, y, z, color=steel_color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面
    theta = np.linspace(0, 2 * np.pi, 30)
    r = np.linspace(0, left_diameter/2, 10)
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = left_cylinder_x + r_grid * np.cos(theta_grid + left_rad)
    y_disk = left_cylinder_y + r_grid * np.sin(theta_grid + left_rad)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (left_cylinder_z + left_length/2)
    ax.plot_surface(x_disk, y_disk, z_bottom, color=steel_color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (left_cylinder_z - left_length/2)
    ax.plot_surface(x_disk, y_disk, z_top, color=steel_color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    line_x = [left_cylinder_x + (left_diameter/2) * np.cos(left_rad), left_cylinder_x + (left_diameter/2) * np.cos(left_rad)]
    line_y = [left_cylinder_y + (left_diameter/2) * np.sin(left_rad), left_cylinder_y + (left_diameter/2) * np.sin(left_rad)]
    line_z = [left_cylinder_z + left_length/2, left_cylinder_z - left_length/2]  # 反转Z轴方向
    ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    # 右侧辊筒 - 逆时针旋转
    right_rad = np.radians(right_angle)
    right_cylinder_x = right_center_x
    right_cylinder_y = right_center_y
    right_cylinder_z = right_center_z
    
    # 创建旋转后的右侧辊筒
    u = np.linspace(0, 2 * np.pi, 30)
    h = np.linspace(-right_length/2, right_length/2, 10)
    x = np.outer(np.cos(u + right_rad), np.ones_like(h)) * (right_diameter/2) + right_cylinder_x
    y = np.outer(np.sin(u + right_rad), np.ones_like(h)) * (right_diameter/2) + right_cylinder_y
    z = np.outer(np.ones_like(u), -h) + right_cylinder_z  # 注意这里的负号，反转Z轴方向
    
    ax.plot_surface(x, y, z, color=rubber_color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面
    theta = np.linspace(0, 2 * np.pi, 30)
    r = np.linspace(0, right_diameter/2, 10)
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = right_cylinder_x + r_grid * np.cos(theta_grid + right_rad)
    y_disk = right_cylinder_y + r_grid * np.sin(theta_grid + right_rad)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (right_cylinder_z + right_length/2)
    ax.plot_surface(x_disk, y_disk, z_bottom, color=rubber_color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (right_cylinder_z - right_length/2)
    ax.plot_surface(x_disk, y_disk, z_top, color=rubber_color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    line_x = [right_cylinder_x + (right_diameter/2) * np.cos(right_rad), right_cylinder_x + (right_diameter/2) * np.cos(right_rad)]
    line_y = [right_cylinder_y + (right_diameter/2) * np.sin(right_rad), right_cylinder_y + (right_diameter/2) * np.sin(right_rad)]
    line_z = [right_cylinder_z + right_length/2, right_cylinder_z - right_length/2]  # 反转Z轴方向
    ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    # 添加接触点
    ax.plot([0], [0], [0], 'ro', markersize=8)
    
    # 添加文字说明
    ax.text(0, left_center_y, left_length/4, f'钢材料辊筒\n长径比={length_ratio}\n顺时针旋转', fontsize=9)
    ax.text(0, right_center_y, right_length/4, f'橡胶材料辊筒\n长径比={length_ratio}\n逆时针旋转', fontsize=9)
    
    # 添加旋转方向指示箭头
    ax.quiver(left_center_x, left_center_y+left_diameter/2, 0, 0, -arrow_length, 0, color='blue', arrow_length_ratio=0.3)
    ax.quiver(right_center_x, right_center_y-right_diameter/2, 0, 0, arrow_length, 0, color='blue', arrow_length_ratio=0.3)
    
    # 添加坐标轴原点标记
    ax.scatter([0], [0], [0], color='red', s=50, marker='o')
    ax.text(0.2, 0.2, 0.2, "原点(0,0,0)", color='red', fontsize=8)
    
    # 使用保存的视角参数，包括旋转角度
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        # 对于不支持roll参数的旧版本
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    
    # 禁用鼠标交互
    ax.mouse_init(rotate_btn=None, zoom_btn=None)
    
    return []

# 创建动画
try:
    ani = FuncAnimation(fig, update, frames=72, interval=100, blit=True)
    plt.show()
except Exception as e:
    # 如果动画创建失败，尝试使用更简单的方式显示
    print(f"动画创建失败: {e}")
    print("尝试使用静态图像显示...")
    update(0)  # 显示第一帧
    plt.show()