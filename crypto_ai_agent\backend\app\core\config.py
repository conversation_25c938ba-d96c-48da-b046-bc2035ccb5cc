import secrets
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, BaseSettings, PostgresDsn, validator


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 项目信息
    PROJECT_NAME: str = "加密货币链上数据分析AI代理"
    
    # 数据库配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "crypto_ai_agent"
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # API密钥配置
    ETHERSCAN_API_KEY: Optional[str] = None
    COINGECKO_API_KEY: Optional[str] = None
    GLASSNODE_API_KEY: Optional[str] = None
    INFURA_API_KEY: Optional[str] = None
    
    # Web3提供者
    WEB3_PROVIDER_URI: str = "https://mainnet.infura.io/v3/"
    
    # 监控的区块链
    SUPPORTED_CHAINS: List[str] = ["ethereum", "binance-smart-chain", "polygon", "avalanche", "solana"]
    
    # 默认策略配置
    DEFAULT_STRATEGIES: List[str] = ["whale_tracking", "developer_activity", "onchain_activity", "smart_contract_interaction", "cross_chain_flow"]
    
    # 模型配置
    MODEL_PATH: str = "app/models"
    
    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()
