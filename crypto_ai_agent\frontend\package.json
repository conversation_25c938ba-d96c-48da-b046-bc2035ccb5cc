{"name": "crypto-ai-agent-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.9", "@mui/material": "^5.14.9", "@tanstack/react-query": "^4.35.3", "axios": "^1.5.0", "chart.js": "^4.4.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "plotly.js": "^2.26.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.16.0", "zustand": "^4.4.1"}, "devDependencies": {"@types/d3": "^7.4.0", "@types/node": "^20.6.2", "@types/plotly.js": "^2.12.26", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-plotly.js": "^2.6.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}