from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean, JSON, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.db.base_class import Base


class StrategyType(str, enum.Enum):
    """策略类型枚举"""
    WHALE_TRACKING = "whale_tracking"
    DEVELOPER_ACTIVITY = "developer_activity"
    ONCHAIN_ACTIVITY = "onchain_activity"
    SMART_CONTRACT_INTERACTION = "smart_contract_interaction"
    CROSS_CHAIN_FLOW = "cross_chain_flow"
    CUSTOM = "custom"


class Strategy(Base):
    """投资策略表"""
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    strategy_type = Column(Enum(StrategyType), nullable=False)
    is_active = Column(Boolean, default=True)
    parameters = Column(JSON)  # 策略参数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    signals = relationship("Signal", back_populates="strategy")


class Signal(Base):
    """投资信号表"""
    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(Integer, ForeignKey("strategy.id"), nullable=False)
    token_id = Column(Integer, ForeignKey("token.id"), nullable=False)
    signal_type = Column(String, nullable=False)  # buy, sell, hold
    strength = Column(Float, nullable=False)  # 信号强度 0-1
    confidence = Column(Float, nullable=False)  # 置信度 0-1
    timestamp = Column(DateTime(timezone=True), nullable=False)
    expiration = Column(DateTime(timezone=True))  # 信号过期时间
    metadata = Column(JSON)  # 附加信息
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    strategy = relationship("Strategy", back_populates="signals")
    token = relationship("Token")
    indicators = relationship("SignalIndicator", back_populates="signal")


class Indicator(Base):
    """指标表"""
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(Text)
    category = Column(String)  # on-chain, market, social, etc.
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关系
    signal_indicators = relationship("SignalIndicator", back_populates="indicator")


class SignalIndicator(Base):
    """信号指标关联表"""
    id = Column(Integer, primary_key=True, index=True)
    signal_id = Column(Integer, ForeignKey("signal.id"), nullable=False)
    indicator_id = Column(Integer, ForeignKey("indicator.id"), nullable=False)
    value = Column(Float, nullable=False)
    weight = Column(Float, nullable=False)  # 指标权重
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    signal = relationship("Signal", back_populates="indicators")
    indicator = relationship("Indicator", back_populates="signal_indicators")


class AnalysisResult(Base):
    """分析结果表"""
    id = Column(Integer, primary_key=True, index=True)
    token_id = Column(Integer, ForeignKey("token.id"), nullable=False)
    analysis_type = Column(String, nullable=False)
    time_period = Column(String, nullable=False)  # daily, weekly, monthly
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    results = Column(JSON, nullable=False)  # 分析结果
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    token = relationship("Token")


class WhaleActivity(Base):
    """鲸鱼活动表"""
    id = Column(Integer, primary_key=True, index=True)
    wallet_id = Column(Integer, ForeignKey("wallet.id"), nullable=False)
    token_id = Column(Integer, ForeignKey("token.id"), nullable=False)
    activity_type = Column(String, nullable=False)  # accumulation, distribution
    amount = Column(String)  # 使用字符串存储大整数
    amount_usd = Column(Float)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    transaction_count = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    wallet = relationship("Wallet")
    token = relationship("Token")
