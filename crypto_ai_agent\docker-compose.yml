version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./reports:/app/reports
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ETHERSCAN_API_KEY=${ETHERSCAN_API_KEY}
      - COINGECKO_API_KEY=${COINGECKO_API_KEY}
      - GLASSNODE_API_KEY=${GLASSNODE_API_KEY}
      - INFURA_API_KEY=${INFURA_API_KEY}
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # Celery Worker - 区块链数据
  celery-blockchain:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery_app worker -Q blockchain_data -l info
    volumes:
      - ./backend:/app
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - ETHERSCAN_API_KEY=${ETHERSCAN_API_KEY}
      - COINGECKO_API_KEY=${COINGECKO_API_KEY}
      - GLASSNODE_API_KEY=${GLASSNODE_API_KEY}
      - INFURA_API_KEY=${INFURA_API_KEY}
    depends_on:
      - db
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # Celery Worker - 分析
  celery-analysis:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery_app worker -Q analysis -l info
    volumes:
      - ./backend:/app
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # Celery Worker - 报告
  celery-reporting:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery_app worker -Q reporting -l info
    volumes:
      - ./backend:/app
      - ./reports:/app/reports
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # Celery Beat - 定时任务
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery_app beat -l info
    volumes:
      - ./backend:/app
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
      - REDIS_HOST=redis
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - db
      - redis
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # Celery Flower - 监控
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
      - celery-blockchain
      - celery-analysis
      - celery-reporting
    networks:
      - app-network
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL数据库
  db:
    image: postgres:14
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=crypto_ai_agent
    ports:
      - "5432:5432"
    networks:
      - app-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7
    ports:
      - "6379:6379"
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
