import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from matplotlib.widgets import Button, Slider

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun']  # 优先使用的中文字体列表
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 设置参数
length_ratio = 13  # 辊筒的长径比
left_diameter = 1.0  # 左侧辊筒直径（单位）
right_diameter = 0.8 * left_diameter  # 右侧辊筒直径略小于左侧
left_length = length_ratio * left_diameter  # 左侧辊筒长度
right_length = left_length  # 右侧辊筒长度与左侧相同

# 创建3D图形和坐标轴，调整图形大小以容纳控制按钮
fig = plt.figure(figsize=(14, 10))
ax = fig.add_subplot(111, projection='3d')

# 设置初始视角
initial_elev = 76
initial_azim = -45
initial_roll = 132  # 添加初始旋转角度
initial_zoom = 1.0  # 添加初始缩放比例

# 保存当前视角参数和缩放状态
view_params = {
    'elev': initial_elev,
    'azim': initial_azim,
    'roll': initial_roll,  # 添加旋转角度参数
    'zoom': initial_zoom,  # 添加缩放参数
    'xlim': (-2, 2),
    'ylim': (-2, 2),
    'zlim': (left_length/2+1, -left_length/2-1)  # 反转Z轴方向
}

# 设置初始坐标轴范围 - 注意这里交换了X和Z轴的范围，并反转了Z轴
ax.set_xlim(view_params['xlim'])
ax.set_ylim(view_params['ylim'])
ax.set_zlim(view_params['zlim'])
ax.set_title('辊筒动画场景 - 3D视图')

# 显示坐标轴
ax.set_xlabel('X轴')
ax.set_ylabel('Y轴')
ax.set_zlabel('Z轴 (长度)')

# 设置等比例显示，确保圆形不变形
try:
    ax.set_box_aspect([4, 4, left_length+2])  # 设置坐标轴比例，注意这里交换了X和Z轴的比例
except AttributeError:
    ax.set_aspect('equal')

# 定义更接近真实材料的颜色
steel_color = '#A9A9A9'  # 钢材料的银灰色
rubber_color = '#1A1A1A'  # 黑色橡胶的深黑色

# 设置多个光源位置和颜色
light_positions = [
    [5, 5, 5],     # 右上方光源
    [-5, -5, 5],   # 左下方光源
    [0, 0, 10],    # 正上方光源
    [10, 0, 0]     # 右侧光源
]
light_colors = [
    '#FFFFFF',  # 白色光源
    '#FFD700',  # 金色光源
    '#ADD8E6',  # 淡蓝色光源
    '#FFA07A'   # 浅珊瑚色光源
]
light_intensities = [1.0, 0.7, 0.8, 0.6]  # 各光源强度

# 计算辊筒位置，使它们靠在一起
left_center_x = 0
left_center_y = 0  # 将左侧辊筒放在Y轴原点
left_center_z = 0
right_center_x = 0
right_center_y = -left_diameter/2 - right_diameter/2  # 右侧辊筒位置，使其与左侧辊筒接触
right_center_z = 0

# 添加新的左侧钢辊筒位置 - 在原左侧辊筒左边，间隙为钢辊筒直径
far_left_center_x = 0  # 左移两个直径（一个是间隙，一个是辊筒自身）
far_left_center_y = left_diameter*2  # 与原左侧辊筒Y坐标相同，保持在同一水平线上
far_left_center_z = left_center_z  # 与原左侧辊筒Z坐标相同

# 创建圆柱体函数 - 添加标记线，轴向为Z轴（反向）
def create_cylinder_with_line(center_x, center_y, center_z, radius, length, color):
    # 圆柱体参数 - 注意这里交换了轴向，现在沿着Z轴延伸（反向）
    u = np.linspace(0, 2 * np.pi, 50)  # 增加分辨率
    h = np.linspace(-length/2, length/2, 20)  # 增加分辨率
    x = np.outer(np.cos(u), np.ones_like(h)) * radius + center_x
    y = np.outer(np.sin(u), np.ones_like(h)) * radius + center_y
    z = np.outer(np.ones_like(u), -h) + center_z  # 注意这里的负号，反转Z轴方向
    
    # 绘制圆柱体表面
    cylinder = ax.plot_surface(x, y, z, color=color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面 - 使用参数方程确保是圆形
    theta = np.linspace(0, 2 * np.pi, 50)  # 增加分辨率
    r = np.linspace(0, radius, 20)  # 增加分辨率
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = center_x + r_grid * np.cos(theta_grid)
    y_disk = center_y + r_grid * np.sin(theta_grid)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (center_z + length/2)  # 反转后的底面
    bottom = ax.plot_surface(x_disk, y_disk, z_bottom, color=color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (center_z - length/2)  # 反转后的顶面
    top = ax.plot_surface(x_disk, y_disk, z_top, color=color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    line_x = [center_x + radius, center_x + radius]
    line_y = [center_y, center_y]
    line_z = [center_z + length/2, center_z - length/2]  # 反转Z轴方向
    line, = ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    return cylinder, bottom, top, line

# 初始创建三个辊筒
far_left_cylinder, far_left_bottom, far_left_top, far_left_line = create_cylinder_with_line(
    far_left_center_x, far_left_center_y, far_left_center_z, left_diameter/2, left_length, steel_color)
left_cylinder, left_bottom, left_top, left_line = create_cylinder_with_line(
    left_center_x, left_center_y, left_center_z, left_diameter/2, left_length, steel_color)
right_cylinder, right_bottom, right_top, right_line = create_cylinder_with_line(
    right_center_x, right_center_y, right_center_z, right_diameter/2, right_length, rubber_color)



# 添加接触点
contact_point, = ax.plot([0], [0], [0], 'ro', markersize=8)

# 添加坐标轴原点标记
ax.scatter([0], [0], [0], color='red', s=50, marker='o')
ax.text(0, 0, 0, "原点(0,0,0)", color='red', fontsize=8)

# 使用保存的视角参数，包括旋转角度
try:
    ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
except TypeError:
    # 对于不支持roll参数的旧版本
    ax.view_init(elev=view_params['elev'], azim=view_params['azim'])

# 添加鼠标事件处理函数，用于更新视图参数
def on_mouse_move(event):
    if event.inaxes == ax:
        # 获取当前视角
        elev, azim = ax.elev, ax.azim
        try:
            roll = ax.roll  # 对于支持roll参数的版本
        except AttributeError:
            roll = view_params['roll']  # 对于不支持roll参数的旧版本
            
        # 更新视图参数
        view_params['elev'] = elev
        view_params['azim'] = azim
        view_params['roll'] = roll
        
        # 更新滑块值（如果存在）
        try:
            elev_slider.set_val(elev)
            azim_slider.set_val(azim)
            roll_slider.set_val(roll)
        except:
            pass  # 如果滑块不存在，忽略错误

# 连接鼠标事件
fig.canvas.mpl_connect('motion_notify_event', on_mouse_move)

# 动画更新函数 - 重新绘制整个辊筒
def update(frame):
    global view_params
    
    # 清除之前的辊筒
    ax.clear()
    
    # 显示坐标轴
    ax.set_xlabel('X轴')
    ax.set_ylabel('Y轴')
    ax.set_zlabel('Z轴 (长度)')
    
    # 重新设置坐标轴范围，使用保存的视图参数
    ax.set_xlim(view_params['xlim'])
    ax.set_ylim(view_params['ylim'])
    ax.set_zlim(view_params['zlim'])
    ax.set_title('辊筒动画场景 - 3D视图')
    
    # 使用更安全的方式设置比例，并应用当前缩放比例
    try:
        ax.set_box_aspect([4/view_params['zoom'], 4/view_params['zoom'], (left_length+2)/view_params['zoom']])
    except AttributeError:
        ax.set_aspect('equal')
    
    # 添加坐标网格，并根据缩放比例调整网格密度
    grid_density = max(2, int(5 * view_params['zoom']))  # 根据缩放比例调整网格密度
    ax.grid(True, linestyle='--', alpha=0.5)
    
    # 设置坐标轴刻度，根据缩放比例调整刻度间隔
    tick_spacing = max(0.5, 1.0 / view_params['zoom'])
    x_ticks = np.arange(view_params['xlim'][0], view_params['xlim'][1] + tick_spacing, tick_spacing)
    y_ticks = np.arange(view_params['ylim'][0], view_params['ylim'][1] + tick_spacing, tick_spacing)
    z_ticks = np.arange(view_params['zlim'][1], view_params['zlim'][0] + tick_spacing, tick_spacing)  # 注意Z轴是反向的
    
    ax.set_xticks(x_ticks)
    ax.set_yticks(y_ticks)
    ax.set_zticks(z_ticks)
    
    # 左侧辊筒顺时针旋转（角度为正值）
    left_angle = frame * 15  # 度数
    
    # 右侧辊筒逆时针旋转（角度为负值）
    right_angle = -frame * 15  # 度数
    
    # 最左侧辊筒顺时针旋转（与左侧辊筒相同）
    far_left_angle = left_angle
    
    # 添加旋转角度显示
    ax.text(0, 0, left_length/2 + 1, f"旋转角度: {left_angle:.1f}°", fontsize=12, color='red')
    
    # 创建旋转后的辊筒 - 使用光照效果
    # 最左侧辊筒 - 顺时针旋转
    far_left_rad = np.radians(far_left_angle)
    far_left_cylinder_x = far_left_center_x
    far_left_cylinder_y = far_left_center_y
    far_left_cylinder_z = far_left_center_z
    
    # 创建旋转后的最左侧辊筒
    u = np.linspace(0, 2 * np.pi, 50)  # 增加分辨率
    h = np.linspace(-left_length/2, left_length/2, 20)  # 增加分辨率
    x = np.outer(np.cos(u + far_left_rad), np.ones_like(h)) * (left_diameter/2) + far_left_cylinder_x
    y = np.outer(np.sin(u + far_left_rad), np.ones_like(h)) * (left_diameter/2) + far_left_cylinder_y
    z = np.outer(np.ones_like(u), -h) + far_left_cylinder_z  # 注意这里的负号，反转Z轴方向
    
    # 计算表面法向量，用于光照计算
    normals_x = np.cos(u + far_left_rad)[:, np.newaxis] * np.ones_like(h)[np.newaxis, :]
    normals_y = np.sin(u + far_left_rad)[:, np.newaxis] * np.ones_like(h)[np.newaxis, :]
    normals_z = np.zeros_like(x)
    
    # 初始化总光照强度
    total_intensity = np.zeros_like(x)
    
    # 计算来自多个光源的光照强度
    for i, light_position in enumerate(light_positions):
        # 计算光照方向
        light_dir_x = light_position[0] - x
        light_dir_y = light_position[1] - y
        light_dir_z = light_position[2] - z
        
        # 归一化光源方向向量
        norm = np.sqrt(light_dir_x**2 + light_dir_y**2 + light_dir_z**2)
        light_dir_x /= norm
        light_dir_y /= norm
        light_dir_z /= norm
        
        # 计算光照强度（点积）
        intensity = light_dir_x * normals_x + light_dir_y * normals_y + light_dir_z * normals_z
        intensity = np.clip(intensity, 0.1, 1.0) * light_intensities[i]  # 应用光源强度
        
        # 累加到总光照强度
        total_intensity += intensity
    
    # 限制总光照强度范围
    total_intensity = np.clip(total_intensity, 0.2, 1.0)
    
    # 创建颜色映射，基于光照强度
    base_color = np.array(matplotlib.colors.to_rgb(steel_color))
    colors = np.zeros((*x.shape, 3))
    
    # 添加条纹图案 - 使用正弦函数创建周期性条纹
    stripe_pattern = np.sin(10 * u + far_left_rad)[:, np.newaxis] * np.ones_like(h)[np.newaxis, :] > 0
    
    # 应用基础颜色和光照
    for i in range(3):
        colors[:, :, i] = base_color[i] * total_intensity
    
    # 应用条纹效果 - 在条纹位置增强亮度
    colors[stripe_pattern] = colors[stripe_pattern] * 1.3
    colors = np.clip(colors, 0, 1)  # 确保颜色值在有效范围内
    
    # 绘制圆柱体表面，使用计算的颜色
    ax.plot_surface(x, y, z, facecolors=colors, shade=False, alpha=0.9)
    
    # 绘制底面和顶面
    theta = np.linspace(0, 2 * np.pi, 50)  # 增加分辨率
    r = np.linspace(0, left_diameter/2, 20)  # 增加分辨率
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = far_left_cylinder_x + r_grid * np.cos(theta_grid + far_left_rad)
    y_disk = far_left_cylinder_y + r_grid * np.sin(theta_grid + far_left_rad)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (far_left_cylinder_z + left_length/2)
    ax.plot_surface(x_disk, y_disk, z_bottom, color=steel_color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (far_left_cylinder_z - left_length/2)
    ax.plot_surface(x_disk, y_disk, z_top, color=steel_color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    #line_x = [far_left_cylinder_x + (left_diameter/2) * np.cos(far_left_rad), far_left_cylinder_x + (left_diameter/2) * np.cos(far_left_rad)]
    #line_y = [far_left_cylinder_y + (left_diameter/2) * np.sin(far_left_rad), far_left_cylinder_y + (left_diameter/2) * np.sin(far_left_rad)]
    #line_z = [far_left_cylinder_z + left_length/2, far_left_cylinder_z - left_length/2]  # 反转Z轴方向
    #ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    # 左侧辊筒 - 顺时针旋转
    left_rad = np.radians(left_angle)
    left_cylinder_x = left_center_x
    left_cylinder_y = left_center_y
    left_cylinder_z = left_center_z
    
    # 创建旋转后的左侧辊筒
    u = np.linspace(0, 2 * np.pi, 50)
    h = np.linspace(-left_length/2, left_length/2, 20)
    x = np.outer(np.cos(u + left_rad), np.ones_like(h)) * (left_diameter/2) + left_cylinder_x
    y = np.outer(np.sin(u + left_rad), np.ones_like(h)) * (left_diameter/2) + left_cylinder_y
    z = np.outer(np.ones_like(u), -h) + left_cylinder_z  # 注意这里的负号，反转Z轴方向
    
    ax.plot_surface(x, y, z, color=steel_color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面
    theta = np.linspace(0, 2 * np.pi, 50)
    r = np.linspace(0, left_diameter/2, 20)
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = left_cylinder_x + r_grid * np.cos(theta_grid + left_rad)
    y_disk = left_cylinder_y + r_grid * np.sin(theta_grid + left_rad)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (left_cylinder_z + left_length/2)
    ax.plot_surface(x_disk, y_disk, z_bottom, color=steel_color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (left_cylinder_z - left_length/2)
    ax.plot_surface(x_disk, y_disk, z_top, color=steel_color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    #line_x = [left_cylinder_x + (left_diameter/2) * np.cos(left_rad), left_cylinder_x + (left_diameter/2) * np.cos(left_rad)]
    #line_y = [left_cylinder_y + (left_diameter/2) * np.sin(left_rad), left_cylinder_y + (left_diameter/2) * np.sin(left_rad)]
    #line_z = [left_cylinder_z + left_length/2, left_cylinder_z - left_length/2]  # 反转Z轴方向
    #ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    # 右侧辊筒 - 逆时针旋转
    right_rad = np.radians(right_angle)
    right_cylinder_x = right_center_x
    right_cylinder_y = right_center_y
    right_cylinder_z = right_center_z
    
    # 创建旋转后的右侧辊筒
    u = np.linspace(0, 2 * np.pi, 50)
    h = np.linspace(-right_length/2, right_length/2, 20)
    x = np.outer(np.cos(u + right_rad), np.ones_like(h)) * (right_diameter/2) + right_cylinder_x
    y = np.outer(np.sin(u + right_rad), np.ones_like(h)) * (right_diameter/2) + right_cylinder_y
    z = np.outer(np.ones_like(u), -h) + right_cylinder_z  # 注意这里的负号，反转Z轴方向
    
    ax.plot_surface(x, y, z, color=rubber_color, alpha=0.7, shade=True)
    
    # 绘制底面和顶面
    theta = np.linspace(0, 2 * np.pi, 50)
    r = np.linspace(0, right_diameter/2, 20)
    theta_grid, r_grid = np.meshgrid(theta, r)
    
    x_disk = right_cylinder_x + r_grid * np.cos(theta_grid + right_rad)
    y_disk = right_cylinder_y + r_grid * np.sin(theta_grid + right_rad)
    
    # 底面（注意Z轴反向）
    z_bottom = np.ones_like(x_disk) * (right_cylinder_z + right_length/2)
    ax.plot_surface(x_disk, y_disk, z_bottom, color=rubber_color, alpha=0.7)
    
    # 顶面（注意Z轴反向）
    z_top = np.ones_like(x_disk) * (right_cylinder_z - right_length/2)
    ax.plot_surface(x_disk, y_disk, z_top, color=rubber_color, alpha=0.7)
    
    # 添加标记线 - 从一端到另一端的直线
    #line_x = [right_cylinder_x + (right_diameter/2) * np.cos(right_rad), right_cylinder_x + (right_diameter/2) * np.cos(right_rad)]
   # line_y = [right_cylinder_y + (right_diameter/2) * np.sin(right_rad), right_cylinder_y + (right_diameter/2) * np.sin(right_rad)]
    #line_z = [right_cylinder_z + right_length/2, right_cylinder_z - right_length/2]  # 反转Z轴方向
    #ax.plot(line_x, line_y, line_z, color='black', linewidth=2)
    
    # 在update函数中
    # 使用保存的视角参数，包括旋转角度
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        # 对于不支持roll参数的旧版本
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    
    return []

# 重置视图
def reset_view(event):
    view_params['elev'] = initial_elev
    view_params['azim'] = initial_azim
    view_params['roll'] = initial_roll
    view_params['zoom'] = initial_zoom
    elev_slider.set_val(view_params['elev'])
    azim_slider.set_val(view_params['azim'])
    roll_slider.set_val(view_params['roll'])
    zoom_slider.set_val(view_params['zoom'])
    
    # 重置缩放
    view_params['xlim'] = (-4, 2)  # 扩大X轴范围以显示最左侧辊筒
    view_params['ylim'] = (-2, 2)
    view_params['zlim'] = (left_length/2+1, -left_length/2-1)
    ax.set_xlim(view_params['xlim'])
    ax.set_ylim(view_params['ylim'])
    ax.set_zlim(view_params['zlim'])
    
    try:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'], roll=view_params['roll'])
    except TypeError:
        ax.view_init(elev=view_params['elev'], azim=view_params['azim'])
    fig.canvas.draw_idle()

# 创建动画
try:
    ani = FuncAnimation(fig, update, frames=72, interval=100, blit=True)
    plt.show()
except Exception as e:
    # 如果动画创建失败，尝试使用更简单的方式显示
    print(f"动画创建失败: {e}")
    print("尝试使用静态图像显示...")
    update(0)  # 显示第一帧
    plt.show()